import OpenAI from 'openai';
const client = new OpenAI({
  apiKey: '3c4635c7-281b-4a28-951d-0b674d7ad600',
  baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  dangerouslyAllowBrowser: true,
});

export async function translateDoubao(message, callback = (res) => {}) {
  console.log(new Date().toISOString(), `start translate`, message);
  if (!message) message = '你好';


  let langText = '英语';

  const updatedHistory = [
    {
      role: 'system',
      content: `# 你是一个翻译工具。用户会输入需要翻译的文本，请你将该文本翻译成指定语言并返回。
      # 请注意用户输入的是通过语音识别而来的文本，可能会有误。所以翻译的时候，可以先修正下可能的错误然后再翻译。
       # 约束：只返回一种常用翻译方式的结果，不要返回多种可能的翻译结果。不要返回除翻译文本以外的任何内容。`,
    },
    {
      role: 'user',
      content: `帮我把接下来发给你的话翻译成${langText}:${message}`,
    },
  ];

  try {
    const stream = await client.chat.completions.create({
      model: 'ep-20241025151753-kps4c',
      messages: updatedHistory,
      stream: true,
    });

    for await (const chunk of stream) {
      const delta = chunk.choices[0]?.delta || {};
      const content = delta.content || '';
      const finish_reason = chunk.choices[0]?.finish_reason;
      const rJson = {
        source_text: message,
        target_text: content,
        finish_reason: finish_reason ? finish_reason : '',
      };
      callback(rJson);
    }
  } catch (error) {
    const rJson = {
      source_text: message,
      target_text: '',
      finish_reason: 'STOP',
      status: 'error',
      message: error.message,
    };
    callback(rJson);
  }
}

class SubtitleMsgData {
  constructor() {
      this.definite = false;
      this.language = '';
      this.paragraph = false;
      this.sequence = 0;
      this.text = '';
      this.userId = '';
  }

  toString() {
      return `SubtitleMsgData{definite=${this.definite}, language='${this.language}', paragraph=${this.paragraph}, sequence=${this.sequence}, text='${this.text}', userId='${this.userId}'}`;
  }
}

function onRoomBinaryMessageReceived(uid, buffer) {
  let subtitles = { value: '' };
  let ret = unpack(buffer, subtitles);
  if (ret) {
      parseData(subtitles.value);
  }
}

function unpack(arraybuffer, subtitles) {
  const kSubtitleHeaderSize = 8;
  
  // Convert ArrayBuffer to Uint8Array
  const message = new Uint8Array(arraybuffer);
  
  // Check if buffer has enough bytes
  // if (message.length < kSubtitleHeaderSize) {
  //     return false;
  // }

  // Check magic number "subv"
  // const magicNumber = (message[0] << 24) | (message[1] << 16) | (message[2] << 8) | message[3];
  // if (magicNumber !== 0x73756276) {
  //     return false;
  // }

  // Get length (4 bytes after magic number)
  const length = (message[4] << 24) | (message[5] << 16) | (message[6] << 8) | message[7];

  // Check if remaining buffer length matches expected length
  // if (message.length - kSubtitleHeaderSize !== length) {
  //     return false;
  // }

  // Read subtitle content
  const subtitleArray = message.slice(kSubtitleHeaderSize);
  const decoder = new TextDecoder('utf-8');
  subtitles.value = decoder.decode(subtitleArray);

  return true;
}

function parseData(msg) {
  try {
      // Parse JSON string
      const jsonData = JSON.parse(msg);
      
      // Store parsed data
      const subtitles = [];

      // Iterate through JSON data and populate structure
      for (const item of jsonData.data) {
          const subData = new SubtitleMsgData();
          subData.definite = item.definite;
          subData.language = item.language;
          subData.paragraph = item.paragraph;
          subData.sequence = item.sequence;
          subData.text = item.text;
          subData.userId = item.userId;
          subtitles.push(subData);
      }

      return subtitles;
  } catch (e) {
      console.error(e);
      return [];
  }
}

export {
  unpack,
  parseData
};